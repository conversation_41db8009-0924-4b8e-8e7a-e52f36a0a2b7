# ThreatMesh API (TO BE UPDATED)

A comprehensive FastAPI service for Attack Surface Mapping (ASM) and Cyber Threat Intelligence (CTI) operations.

## 🚀 Features

### Attack Surface Mapping (ASM)
- **Subdomain Enumeration** - Discover subdomains using multiple sources
- **Public IP Search** - Find public-facing infrastructure 
- **Webserver Discovery** - Identify web services and technologies
- **Technology Detection** - Analyze technology stacks using Wappalyzer
- **Stack Vulnerabilities** - Identify vulnerabilities in detected technologies

### Cyber Threat Intelligence (CTI)

- **Dark Web Leaks** - Search for data breaches and leaks
- **Git Search** - Scan repositories for exposed secrets using git-hound
- **Pastes Search** - Monitor paste sites for sensitive information
- **Phishing Domains** - Detect potential phishing domains

## 🏗️ Architecture

- **FastAPI** - Modern, fast web framework for building APIs
- **Celery** - Distributed task queue for background processing
- **MongoDB 7.0** - Document database for storing results
- **Redis 7.2** - Message broker and caching layer
- **Docker** - Containerized deployment with health checks

### 🗂️ Project Structure

```
threatmesh/
├── app/                    # Main application package
│   ├── db/                # Database connections and operations
│   ├── models/            # Pydantic data models and schemas
│   ├── routers/           # API route handlers
│   │   ├── asm/          # Attack Surface Mapping endpoints
│   │   └── cti/          # Cyber Threat Intelligence endpoints
│   ├── tasks/            # Celery background tasks
│   │   ├── asm/          # ASM task implementations
│   │   └── cti/          # CTI task implementations
│   ├── utils/            # Utility functions and helpers
│   ├── data/             # Static data and configurations
│   ├── config.py         # Application configuration
│   ├── main.py           # FastAPI application setup
│   └── worker.py         # Celery worker configuration
├── docker-compose.yml     # Docker Compose configuration
├── Dockerfile            # Container build instructions
├── requirements.txt      # Python dependencies
├── env.example          # Environment variables template
├── DOCKER_SETUP.md      # Comprehensive Docker setup guide
└── README.md           # This file
```

## 🚀 Quick Start (Recommended)

The easiest way to run ThreatMesh is using Docker Compose:

### Prerequisites
- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 4GB RAM available

### Setup Steps

1. **Clone the repository**:
   ```bash
   <NAME_EMAIL>:CurlSek/threatmesh.git
   cd threatmesh
   ```

2. **Configure environment**:
   ```bash
   cp env.example .env
   # Edit .env file and add your API keys
   ```

3. **Start all services**:
   ```bash
   docker-compose up --build -d
   ```

4. **Verify deployment**:
   - API: http://localhost:8000
   - API Docs: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health

For detailed Docker setup instructions, see [DOCKER_SETUP.md](DOCKER_SETUP.md).

## 🔧 Manual Installation (Advanced) - INCOMPLETE 

For development or custom deployments:

### System Prerequisites

**Security Tools** (installed in Docker automatically):
```bash
# Go tools for subdomain enumeration
go install -v github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest
go install -v github.com/projectdiscovery/httpx/cmd/httpx@latest

# Git-hound for repository scanning
wget -O /usr/local/bin/git-hound https://github.com/CurlSek/git-hound/releases/download/nightly/git-hound
chmod +x /usr/local/bin/git-hound

# Node.js for Wappalyzer
sudo apt install nodejs npm
sudo npm install -g yarn
```

**Python Dependencies**:
```bash
sudo apt install python3-dev build-essential
```

### Application Setup

1. **Clone and setup environment**:
   ```bash
   git clone https://github.com/yourusername/threatmesh.git
   cd threatmesh
   python -m venv venv
   source venv/bin/activate  # Windows: venv\Scripts\activate
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment**:
   ```bash
   cp env.example .env
   # Edit .env with your configuration
   ```

4. **Start external services**:
   ```bash
   # MongoDB
   mongod --dbpath /path/to/data/db
   
   # Redis
   redis-server
   ```

5. **Run the application**:
   ```bash
   # Start API server
   python run.py
   
   # Start worker (separate terminal)
   python -m app.worker
   ```

## 📚 API Usage

### Authentication
All endpoints require an API key in the request header:
```bash
X-API-Key: your-api-key-here
```

### Example Requests

**Subdomain Enumeration**:
```bash
curl -H "X-API-Key: your-key" \
     "http://localhost:8000/asm/subdomain-enum?domain=example.com"
```

**Technology Detection**:
```bash
curl -H "X-API-Key: your-key" \
     "http://localhost:8000/asm/tech-detection?domain=example.com"
```

**Git Repository Search**:
```bash
curl -H "X-API-Key: your-key" \
     "http://localhost:8000/cti/git-search?query=example.com"
```

### API Documentation
- **Interactive Docs**: http://localhost:8000/docs
- **OpenAPI Schema**: http://localhost:8000/openapi.json

## 🛠️ Development

### Running in Development Mode
```bash
# API with auto-reload
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Worker with auto-reload
watchmedo auto-restart --directory=./app --pattern=*.py --recursive -- python -m app.worker
```

### Testing
```bash
# Run tests
python run_tests.py

# With coverage
python -m pytest --cov=app tests/
```

## 📊 Monitoring & Operations

### Health Checks
```bash
# Service health
curl http://localhost:8000/health

# Docker container status
docker-compose ps
```

### Logs
```bash
# View all logs
docker-compose logs

# Follow specific service
docker-compose logs -f api
docker-compose logs -f worker
```

### Scaling Workers
```bash
# Scale to 3 worker instances
docker-compose up --scale worker=3
```


## 🔄 Task Caching

ThreatMesh includes intelligent result caching with different expiration times:
- **Subdomain Enumeration**: 24 hours
- **Technology Detection**: 30 days  
- **Stack Vulnerabilities**: 7 days
- **Other operations**: 6-12 hours

This prevents unnecessary re-scanning and improves response times for recent queries.

