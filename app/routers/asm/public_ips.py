"""
Public IP search route.
"""
import logging
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from datetime import datetime

from fastapi import APIRouter, HTTPException, status, Query

from app.db import operations
from app.models.task import TaskStatus, ExecutionResponse
from app.models.public_ips_request import PublicIpsRequest
from app.tasks.asm.public_ip_search import run_public_ip_search
from app.utils.response_utils import create_task_response

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/public_ips", response_model=ExecutionResponse)
async def post_public_ips(request: PublicIpsRequest) -> ExecutionResponse:
    """Start public IPs discovery for a domain

    Request Body:
    {
        "domain": "example.com",
        "depth": 0  # Number of pages to fetch from Shodan API (0 for unlimited)
    }
    """

    try:
        # Extract domain and depth from request
        domain = request.domain
        depth = request.depth

        # Start a new task
        logger.info(f"Starting new public IP search for domain {domain}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="public_ips",
            parameters={"domain": domain, "depth": depth}
        )

        # Start the task - pass the job_id to the Celery task
        run_public_ip_search.delay(domain, job_id, depth)

        # Return lightweight execution response
        return ExecutionResponse(
            job_id=job_id,
            success=True,
            feature="public_ips",
            created_at=datetime.now()
        )
    except Exception as e:
        logger.exception(f"Error starting public IP search task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}",
        )
