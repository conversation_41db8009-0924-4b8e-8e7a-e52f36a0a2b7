"""
ASM Stack Vulnerabilities Router
"""
from fastapi import APIRouter, HTTPException, status
from datetime import datetime
import logging

from app.db import operations
from app.models.task import TaskStatus, ExecutionResponse
from app.models.stack_vulnerabilities_request import StackVulnerabilitiesRequest
from app.tasks.asm.stack_vulnerabilities import run_stack_vulnerabilities

logger = logging.getLogger(__name__)

router = APIRouter()





@router.post("/stack-vulns", response_model=ExecutionResponse)
async def post_stack_vulnerabilities(request: StackVulnerabilitiesRequest) -> ExecutionResponse:
    """
    Start stack vulnerabilities analysis for a domain or CPE.

    **Request Body:**
    - `domain`: Domain to analyze tech stack from previous detection results
    - `cpe`: CPE string to directly analyze for vulnerabilities

    **Requirements:**
    - Either domain or cpe must be provided
    - For domain analysis: Previous tech detection results must exist
    - CPE format: cpe:2.3:part:vendor:product:version:update:edition:language:sw_edition:target_sw:target_hw:other
    """
    try:
        # Extract domain and cpe from request
        domain = request.domain
        cpe = request.cpe
        # Start a new task
        logger.info(f"Starting new stack vulnerabilities analysis for domain={domain}, cpe={cpe}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="stack_vulnerabilities",
            parameters={"domain": domain, "cpe": cpe}
        )

        # Start the task - pass the job_id to the Celery task
        run_stack_vulnerabilities.delay(domain=domain, cpe=cpe, job_id=job_id)

        # Return lightweight execution response
        return ExecutionResponse(
            job_id=job_id,
            success=True,
            feature="stack_vulnerabilities",
            created_at=datetime.now()
        )

    except Exception as e:
        logger.exception(f"Error in stack vulnerabilities POST endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}"
        )


 