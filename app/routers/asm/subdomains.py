"""
Subdomain enumeration route.
"""
import logging
from typing import Op<PERSON>, <PERSON><PERSON>
from datetime import datetime

from fastapi import APIRouter, HTTPException, status, Query

from app.db import operations
from app.models.task import TaskStatus, ExecutionResponse
from app.models.subdomain_request import SubdomainRequest
from app.tasks.asm.subdomain_enumeration import run_subdomain_enumeration
from app.utils.response_utils import create_task_response


logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/subdomains", response_model=ExecutionResponse)
async def post_subdomains(request: SubdomainRequest) -> ExecutionResponse:
    """Start subdomain enumeration for a domain

    Request Body:
    {
        "domain": "example.com",
        "quick": false
    }

    Parameters:
    - domain: Target domain to enumerate subdomains for
    - quick: If true, uses faster enumeration with limited sources (default: false)
    """

    try:
        # Extract parameters from request
        domain = request.domain
        quick = request.quick

        # Start a new task
        logger.info(f"Starting new subdomain enumeration for domain {domain} (quick={quick})")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="subdomain_enumeration",
            parameters={"domain": domain, "quick": quick}
        )

        # Start the task - pass the job_id to the Celery task
        run_subdomain_enumeration.delay(domain, job_id, quick=quick)

        # Return lightweight execution response
        return ExecutionResponse(
            job_id=job_id,
            success=True,
            feature="subdomain_enumeration",
            created_at=datetime.now()
        )
    except Exception as e:
        logger.exception(f"Error starting subdomain enumeration task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}",
        )
