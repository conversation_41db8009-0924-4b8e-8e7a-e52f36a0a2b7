"""
ASM Technology Detection Router
"""
from fastapi import APIRouter, HTTPException, status, Query, Depends
from typing import Optional, Dict, Any, Tuple
from datetime import datetime
from math import ceil
import logging

from app.db import operations
from app.models.tech_detection_request import TechDetectionPostRequest, TechDetectionRequest
from app.models.task import TaskStatus, ExecutionResponse, TaskResponse

from app.tasks.asm.tech_detection import run_tech_detection

from app.utils.response_utils import create_task_response

logger = logging.getLogger(__name__)

router = APIRouter()


def _get_paginated_tech_results(
    job_id: str, 
    operation: Dict[str, Any], 
    url: Optional[str] = None, 
    page: int = 1, 
    records_per_page: int = 100
) -> TaskResponse:
    """
    Helper function to get paginated technology detection results.
    
    Args:
        job_id: The job ID
        operation: The operation data
        url: Optional URL filter
        page: Page number
        records_per_page: Records per page
        
    Returns:
        TaskResponse with paginated results
    """
    from app.db.operations import get_tech_detection_chunk, get_tech_detection_results_by_url
    
    response = create_task_response(operation)
    
    if not response.results:
        response.results = {}
    
    # Base statistics
    base_results = {
        "statistics": operation.get("structured_output", {}).get("statistics", {}),
        "pagination": {
            "page": page,
            "records_per_page": records_per_page
        }
    }
    
    # URL-filtered results
    if url:
        all_results = get_tech_detection_results_by_url(job_id, url)
        
        # Apply pagination
        start_idx = (page - 1) * records_per_page
        end_idx = start_idx + records_per_page
        paginated_results = all_results[start_idx:end_idx]
        
        # Calculate total pages
        total_pages = ceil(len(all_results) / records_per_page) if len(all_results) > 0 else 1
        
        response.results.update({
            **base_results,
            "tech_detections": paginated_results,
            "pagination": {
                **base_results["pagination"],
                "total_records": len(all_results),
                "total_pages": total_pages
            },
            "url_filter": url
        })
    else:
        # Standard chunked pagination
        chunk_number = page - 1
        chunk = get_tech_detection_chunk(job_id, chunk_number)
        
        if not chunk:
            tech_detections = []
            total_records = 0
        else:
            tech_detections = chunk.get("data", [])
            total_records = len(tech_detections)
        
        limited_detections = tech_detections[:records_per_page]
        total_pages = ceil(total_records / records_per_page) if total_records > 0 else 1
        
        response.results.update({
            **base_results,
            "tech_detections": limited_detections,
            "pagination": {
                **base_results["pagination"],
                "total_records": total_records,
                "total_pages": total_pages
            }
        })
    
    return response


def _handle_completed_operation(
    job_id: str,
    url: Optional[str] = None,
    page: int = 1,
    records_per_page: int = 100
) -> TaskResponse:
    """
    Helper function to handle completed operations with pagination.
    
    Args:
        job_id: The job ID
        url: Optional URL filter
        page: Page number
        records_per_page: Records per page
        
    Returns:
        TaskResponse with paginated results
    """
    operation = operations.get_operation(job_id)
    return _get_paginated_tech_results(job_id, operation, url, page, records_per_page)



@router.post("/tech-detection", response_model=ExecutionResponse)
async def post_tech_detection(request: TechDetectionRequest) -> ExecutionResponse:
    """
    Start technology detection analysis for a domain.

    **Request Body:**
    - `domain`: Domain to analyze webserver URLs from previous discovery

    **Requirements:**
    - Previous webserver discovery results must exist for the domain
    """
    try:
        # Extract domain from request
        domain = request.domain

        # Start a new task
        logger.info(f"Starting new technology detection for domain {domain}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="tech_detection",
            parameters={"domain": domain}
        )

        # Start the task - pass the job_id to the Celery task
        run_tech_detection.delay(domain=domain, job_id=job_id)

        # Return lightweight execution response
        return ExecutionResponse(
            job_id=job_id,
            success=True,
            feature="tech_detection",
            created_at=datetime.now()
        )

    except Exception as e:
        logger.exception(f"Error in tech detection POST endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}"
        )

@router.post("/tech-detection_custom", response_model=ExecutionResponse)
async def post_tech_detection_custom(request: TechDetectionPostRequest) -> ExecutionResponse:
    """
    Start technology detection analysis on provided URLs.

    **Request Body:**
    - `urls`: List of URLs to analyze (max 100)
    """
    try:
        # Extract URLs from request
        urls = request.urls

        # Start a new task
        logger.info(f"Starting new technology detection for {len(urls)} URLs")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="tech_detection",
            parameters={"urls": urls}
        )

        # Start the task - pass the job_id to the Celery task
        run_tech_detection.delay(urls=urls, job_id=job_id)

        # Return lightweight execution response
        return ExecutionResponse(
            job_id=job_id,
            success=True,
            feature="tech_detection",
            created_at=datetime.now()
        )

    except Exception as e:
        logger.exception(f"Error in tech detection POST endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}"
        )