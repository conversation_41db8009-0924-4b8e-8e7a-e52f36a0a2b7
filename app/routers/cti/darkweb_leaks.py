"""
DarkWeb leaks search routes.
"""
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime
from math import ceil

from fastapi import APIRouter, HTTPException, status, Query

from app.db import operations
from app.models.task import TaskStatus, ExecutionResponse
from app.models.darkweb_leaks_request import DarkwebLeaksRequest
from app.tasks.cti.darkweb_leaks import run_darkweb_search_by_domain
from app.utils.response_utils import create_task_response


logger = logging.getLogger(__name__)

router = APIRouter(prefix="/darkweb_leaks")


@router.post("", response_model=ExecutionResponse)
async def darkweb_leaks_search(request: DarkwebLeaksRequest) -> ExecutionResponse:
    """
    Start darkweb leaks search for a domain.

    Request Body:
    {
        "domain": "example.com"
    }
    """
    try:
        # Extract domain from request
        domain = request.domain

        # Start a new task
        logger.info(f"Starting new darkweb leaks search for domain {domain}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="darkweb_leaks",
            parameters={"domain": domain}
        )

        # Start the task - pass the job_id to the Celery task
        task = run_darkweb_search_by_domain.delay(domain, job_id, chunk_size=100)
        logger.info(f"Celery task started with task_id {task.id}")

        # Return lightweight execution response
        return ExecutionResponse(
            job_id=job_id,
            success=True,
            feature="darkweb_leaks",
            created_at=datetime.now()
        )
    except Exception as e:
        logger.exception(f"Error in darkweb leaks search: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error in darkweb leaks search: {str(e)}"
        )


