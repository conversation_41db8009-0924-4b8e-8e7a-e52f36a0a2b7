"""
Public IP search task.
"""
import logging
import json
import requests
import os
from datetime import datetime
from dotenv import load_dotenv

from app.db import operations
from app.models.task import TaskStatus
from app.tasks.celery_app import celery_app
from app.tasks.executor import handle_task_success, handle_task_failure, handle_task_exception

# Load environment variables
load_dotenv()

# Shodan API Configuration
SHODAN_API_KEY = os.getenv("SHODAN_API_KEY")
SHODAN_API_URL = "https://api.shodan.io/shodan/host/search"

logger = logging.getLogger(__name__)

def query_shodan_api(query: str, page: int = 1):
    """Query Shodan API for host information.

    Args:
        query: The search query
        page: Page number for pagination

    Returns:
        dict: The Shodan API response data
    """
    try:
        params = {
            'key': SHODAN_API_KEY,
            'query': query,
            'page': page
        }
        
        response = requests.get(SHODAN_API_URL, params=params, timeout=30)
        response.raise_for_status()
        
        return response.json()
    except Exception as e:
        logger.error(f"Error querying Shodan API: {str(e)}")
        return None

@celery_app.task(bind=True)
def run_public_ip_search(self, domain: str, job_id: str = None, depth: int = 0):
    """Search for public IPs associated with a domain using Shodan API.

    Args:
        domain: The domain to search for
        job_id: Optional job ID for an existing operation
        depth: Number of pages to fetch from Shodan API (0 for unlimited, default: 0)
    """
    # Ensure depth is an integer (handle string from API request)
    try:
        depth = int(depth)
    except Exception:
        depth = 0

    # Create operation if needed
    if not job_id:
        job_id = operations.create_operation(
            feature="public_ips",
            parameters={"domain": domain, "depth": depth}
        )

    # Mark as started in database and store Celery task ID
    operations.update_operation(
        job_id=job_id,
        status=TaskStatus.RUNNING,
        start_time=datetime.now(),
        progress_perc=0,
        task_id=self.request.id
    )

    # Update task state in Celery
    self.update_state(state='STARTED', meta={'progress': 0, 'message': 'Starting public IP search'})

    try:
        # Shodan queries for the domain
        shodan_queries = [
            f"ssl.cert.subject.cn:{domain}",
            f"hostname:{domain}"
        ]

        all_ips = set()
        shodan_ips = set()
        http_errors = []
        any_query_succeeded = False
        ip_details = {}

        # Process each Shodan query
        for query in shodan_queries:
            try:
                page = 1
                total_pages = None
                while True:
                    # Break if we've reached the depth limit (if specified)
                    if depth > 0 and page > depth:
                        break

                    api_data = query_shodan_api(query, page)

                    if api_data and 'matches' in api_data:
                        any_query_succeeded = True
                        # Extract IPs and details from matches
                        for match in api_data['matches']:
                            if 'ip_str' in match:
                                ip = match['ip_str']
                                all_ips.add(ip)
                                shodan_ips.add(ip)
                                
                                if ip not in ip_details:
                                    ip_details[ip] = {
                                        "ip": ip,
                                        "source": "shodan",
                                        "ports": [],
                                        "services": [],
                                        "hostnames": [],
                                        "tags": [],
                                        "vulns": [],
                                        "cpes": [],
                                        "location": {},
                                        "organization": None,
                                        "isp": None,
                                        "asn": None,
                                        "domains": []
                                    }
                                    
                                    # Extract basic information
                                    if 'port' in match:
                                        ip_details[ip]["ports"].append(match['port'])
                                    
                                    if 'hostnames' in match:
                                        ip_details[ip]["hostnames"] = match['hostnames']
                                    
                                    if 'domains' in match:
                                        ip_details[ip]["domains"] = match['domains']
                                    
                                    if 'cpe' in match:
                                        ip_details[ip]["cpes"] = match['cpe']
                                    
                                    if 'org' in match:
                                        ip_details[ip]["organization"] = match['org']
                                    
                                    if 'isp' in match:
                                        ip_details[ip]["isp"] = match['isp']
                                    
                                    if 'asn' in match:
                                        ip_details[ip]["asn"] = match['asn']
                                    
                                    if 'location' in match:
                                        ip_details[ip]["location"] = {
                                            "city": match['location'].get('city'),
                                            "region": match['location'].get('region_code'),
                                            "country": match['location'].get('country_name'),
                                            "country_code": match['location'].get('country_code'),
                                            "latitude": match['location'].get('latitude'),
                                            "longitude": match['location'].get('longitude')
                                        }
                                    
                                    # Extract service information
                                    service_info = {
                                        "name": match.get('product', ''),
                                        "version": match.get('version', ''),
                                        "transport": match.get('transport', ''),
                                        "port": match.get('port', '')
                                    }
                                    
                                    # Add HTTP information if available
                                    if 'http' in match:
                                        http_info = match['http']
                                        service_info.update({
                                            "server": http_info.get('server'),
                                            "title": http_info.get('title'),
                                            "components": http_info.get('components', {})
                                        })
                                    
                                    ip_details[ip]["services"].append(service_info)

                                logger.debug(f"Found IP: {ip}")
                        # If no matches in this page, no need to continue pagination
                        if not api_data['matches']:
                            break
                        # Set total_pages for progress calculation
                        if total_pages is None:
                            if depth > 0:
                                total_pages = depth
                            elif 'total' in api_data:
                                total_pages = (api_data['total'] + 99) // 100
                            else:
                                total_pages = 1
                        progress = min(100, int((page / total_pages) * 100))
                        self.update_state(state='STARTED', meta={
                            'progress': progress,
                            'message': f'Processing page {page} of query: {query}'
                        })
                    else:
                        # If API call failed, break pagination
                        break
                    page += 1
            except Exception as e:
                error_msg = f"Error processing Shodan query {query}: {str(e)}"
                logger.error(error_msg)
                http_errors.append(error_msg)

        # If all queries failed, mark the task as failed
        if not any_query_succeeded:
            error_message = "All Shodan queries failed"
            self.update_state(state='FAILURE', meta={'message': error_message})
            return handle_task_failure(job_id, error_message)

        # Prepare the structured output
        structured_output = {
            "data": list(ip_details.values()),
            "stats": {
                "total": len(all_ips),
                "sources": {
                    "shodan": len(shodan_ips),
                    "censys": 0,
                    "other": 0
                },
                "scan_parameters": {
                    "domain": domain,
                    "depth": depth
                }
            }
        }

        self.update_state(state='STARTED', meta={'progress': 100, 'message': 'Finalizing results'})

        # Use helper function for success
        return handle_task_success(
            job_id=job_id,
            structured_output=structured_output,
            message=f"Found {len(all_ips)} IPs for domain {domain}",
            count=len(all_ips)
        )

    except Exception as e:
        self.update_state(state='FAILURE', meta={'message': str(e)})
        # Use helper function for exceptions
        return handle_task_exception(job_id, e, f"public IP search for domain {domain}")
