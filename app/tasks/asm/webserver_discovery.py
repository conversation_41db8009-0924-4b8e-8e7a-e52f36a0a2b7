"""
Webserver discovery task.
"""
import logging
import json
import tempfile
import os
from datetime import datetime
from typing import List, Dict, Any, Set

from app.db import operations
from app.models.task import TaskStatus
from app.tasks.celery_app import celery_app
from app.tasks.executor import execute_command, handle_task_success, handle_task_failure, handle_task_exception

logger = logging.getLogger(__name__)

@celery_app.task(bind=True)
def run_webserver_discovery(self, domain: str = None, subdomains: List[str] = None, job_id: str = None, chunk_size: int = 50, filter_status_code: str = None):
    """Run webserver discovery for a list of subdomains using httpx"""
    try:
        # Create operation if needed
        if not job_id:
            parameters = {}
            if domain:
                parameters["domain"] = domain
            if subdomains:
                parameters["subdomains"] = subdomains
            if filter_status_code:
                parameters["filter_status_code"] = filter_status_code

            job_id = operations.create_operation(
                feature="webserver_discovery",
                parameters=parameters
            )

        # Mark as started in database and store Celery task ID
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.RUNNING,
            start_time=datetime.now(),
            task_id=self.request.id  # Store Celery's task ID
        )

        # Update task state in Celery
        self.update_state(state='STARTED', meta={'progress': 0, 'message': 'Starting webserver discovery'})

        # Get subdomains list
        if domain and not subdomains:
            # Get subdomains from previous subdomain enumeration results
            subdomains = _get_subdomains_from_db(domain)
            if not subdomains:
                error_msg = f"No subdomain enumeration results found for domain {domain}. Please run subdomain enumeration first."
                operations.update_operation(
                    job_id=job_id,
                    status=TaskStatus.FAILED,
                    error=error_msg,
                    end_time=datetime.now()
                )
                return {"job_id": job_id, "success": False, "message": error_msg}

        if not subdomains:
            error_msg = "No subdomains provided for webserver discovery"
            operations.update_operation(
                job_id=job_id,
                status=TaskStatus.FAILED,
                error=error_msg,
                end_time=datetime.now()
            )
            return {"job_id": job_id, "success": False, "message": error_msg}

        # Update progress
        self.update_state(state='STARTED', meta={'progress': 10, 'message': f'Found {len(subdomains)} subdomains to check'})

        # Create temporary file with subdomains
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            for subdomain in subdomains:
                f.write(f"{subdomain}\n")
            temp_file = f.name

        try:
            # Update progress
            self.update_state(state='STARTED', meta={'progress': 20, 'message': 'Running httpx scan'})

            # Execute httpx command
            httpx_cmd = [
                "/opt/httpx",
                "-l", temp_file,
                "-json",
                "-silent",
                "-random-agent",
                # "-ports", "80,443,8009,8180,81,300,591,593,832,981,1010,1311,2082,2087,2095,2096,2480,3000,3128,3333,4243,4567,4711,4712,4993,5000,5104,5108,5800,6543,7000,7396,7474,8000,8001,8008,8014,8042,8069,8080,8081,8088,8090,8091,8118,8123,8172",
                "-duc",
                "-retries", "2",
                "-timeout", "20"
            ]

            # Add filter-code parameter if provided
            if filter_status_code:
                httpx_cmd.extend(["-filter-code", filter_status_code])
            success, output_lines = execute_command(job_id, httpx_cmd)

            # Update progress
            self.update_state(state='STARTED', meta={'progress': 70, 'message': 'Processing results'})

            # Process results if successful
            if success:
                # Parse JSON output from httpx
                current_chunk = []
                chunk_number = 0
                protocols = {"http": 0, "https": 0}
                status_codes = {}
                tech_stack = set()
                total_webservers = 0

                # Process output lines directly (no need to split)
                for line in output_lines:
                    try:
                        data = json.loads(line)
                        
                        # Extract all available information from httpx output
                        webserver_info = {
                            "url": data.get("url", ""),
                            "location": data.get("location", ""),
                            "title": data.get("title", ""),
                            "webserver": data.get("webserver", ""),
                            "content_type": data.get("content_type", ""),
                            "response_time": data.get("time", ""),  # Response time
                            "a_records": data.get("a", []),  # IPv4 addresses
                            "aaaa_records": data.get("aaaa", []),  # IPv6 addresses
                            "tech": data.get("tech", []),
                            "words": data.get("words", 0),
                            "lines": data.get("lines", 0),
                            "status_code": data.get("status_code", 0),
                            "content_length": data.get("content_length", 0),
                            "cdn": data.get("cdn", False),
                            "cdn_name": data.get("cdn_name", ""),
                            "cdn_type": data.get("cdn_type", ""),
                            "knowledgebase": data.get("knowledgebase", {}),
                        }
                        
                        # Add to current chunk
                        current_chunk.append(webserver_info)
                        total_webservers += 1
                        
                        # Store chunk when full
                        if len(current_chunk) >= chunk_size:
                            operations.store_webserver_discovery_chunk(job_id, domain or "mixed_domains", chunk_number, current_chunk)
                            chunk_number += 1
                            current_chunk = []
                        
                        # Collect statistics
                        scheme = data.get("scheme", "")
                        if scheme in protocols:
                            protocols[scheme] += 1
                            
                        status_code = data.get("status_code", 0)
                        if status_code:
                            status_codes[str(status_code)] = status_codes.get(str(status_code), 0) + 1
                            
                        # Collect technologies
                        if data.get("tech"):
                            tech_stack.update(data.get("tech", []))
                            
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse JSON: {line}, error: {e}")
                        continue

                # Store any remaining records in the last chunk
                if current_chunk:
                    operations.store_webserver_discovery_chunk(job_id, domain or "mixed_domains", chunk_number, current_chunk)
                    chunk_number += 1

                # Update progress
                self.update_state(state='STARTED', meta={'progress': 90, 'message': 'Finalizing results'})

                # Prepare structured output with stats
                structured_output = {
                    "stats": {
                        "total": total_webservers,
                        "total_chunks": chunk_number,
                        "chunk_size": chunk_size,
                        "protocols": protocols,
                        "status_codes": status_codes,
                        "technologies": list(tech_stack),
                        "total_subdomains_checked": len(subdomains),
                        "responsive_servers": total_webservers
                    }
                }

                # Use helper function for success
                return handle_task_success(
                    job_id=job_id,
                    structured_output=structured_output,
                    message=f"Found {total_webservers} responsive web servers from {len(subdomains)} subdomains",
                    count=total_webservers
                )
            else:
                # Use helper function for command failure
                error_message = '\n'.join(output_lines) if output_lines else "Command execution failed"
                return handle_task_failure(job_id, error_message)

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file)
            except Exception as e:
                logger.warning(f"Failed to clean up temporary file {temp_file}: {e}")

    except Exception as e:
        # Use helper function for exceptions
        return handle_task_exception(job_id, e, "webserver discovery")


def _get_subdomains_from_db(domain: str) -> List[str]:
    """
    Get subdomains from the most recent successful subdomain enumeration for a domain.
    Now uses chunked storage to retrieve all subdomains.

    Args:
        domain: Domain to get subdomains for

    Returns:
        List[str]: List of subdomains or empty list if none found
    """
    try:
        # Find the most recent completed subdomain enumeration for this domain
        operation = operations.operations_collection.find_one(
            {
                "feature": "subdomain_enumeration",
                "parameters.domain": domain,
                "status": TaskStatus.COMPLETED,
                "structured_output": {"$exists": True}
            },
            sort=[("updated_at", -1)]  # Get most recent
        )

        if not operation:
            logger.warning(f"No completed subdomain enumeration found for domain {domain}")
            return []

        structured_output = operation.get("structured_output", {})
        stats = structured_output.get("stats", {})

        # Get subdomains from chunked storage
        if stats.get("total_chunks", 0) > 0:
            job_id = operation["_id"]
            total_chunks = stats.get("total_chunks", 0)
            all_subdomains = []

            for chunk_number in range(total_chunks):
                chunk_data = operations.get_subdomain_enumeration_chunk(job_id, chunk_number)
                if chunk_data and "data" in chunk_data:
                    all_subdomains.extend(chunk_data["data"])

            logger.info(f"Retrieved {len(all_subdomains)} subdomains from {total_chunks} chunks for domain {domain}")
            return all_subdomains

        logger.warning(f"No subdomains found for domain {domain}")
        return []

    except Exception as e:
        logger.error(f"Error retrieving subdomains for domain {domain}: {e}")
        return []