import subprocess
import logging
import time
import os
import signal
from datetime import datetime
from typing import Dict, <PERSON>, Tuple, Optional, Any
import threading

from app.db import operations
from app.models.task import TaskStatus, Task, TaskResult

logger = logging.getLogger(__name__)

# Dictionary to store running processes
running_processes: Dict[str, subprocess.Popen] = {}
process_lock = threading.Lock()

def execute_command(job_id: str, command: List[str], timeout: int = 3600) -> Tuple[bool, List[str]]:
    """Execute a command and return output as list of lines"""
    process = None  # Initialize here to avoid NameError in finally
    try:
        # Make sure operation exists
        operation = operations.get_operation(job_id)
        if not operation:
            # Create operation if it doesn't exist
            operations.create_operation(
                feature="unknown",  # This should be set by the caller
                parameters={},
                job_id=job_id
            )

        # Initialize raw_output if it doesn't exist
        if not operation or "raw_output" not in operation:
            operations.update_operation(
                job_id=job_id,
                raw_output=[]
            )

        # Start process
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True,
            start_new_session=True,
        )

        # Store process
        with process_lock:
            running_processes[job_id] = process

        # Read output
        start_time = time.time()
        raw_output = []

        while True:
            # Check if process has timed out
            if time.time() - start_time > timeout:
                kill_task(job_id)
                error_msg = f"Task timed out after {timeout} seconds"
                logger.error(error_msg)
                operations.update_operation(
                    job_id=job_id,
                    status=TaskStatus.KILLED,
                    end_time=datetime.now(),
                    error=error_msg
                )
                return False, [error_msg]

            # Read line from process
            line = process.stdout.readline()
            if not line and process.poll() is not None:
                break

            if line:
                line = line.strip()
                # Add line to raw_output
                raw_output.append(line)

        # Process completed
        return_code = process.wait()

        # Only store raw_output in DB if there was an error
        if return_code != 0:
            for line in raw_output:
                operations.add_raw_output(job_id, line)

        # Return success and output as list
        return return_code == 0, raw_output

    except Exception as e:
        logger.exception(f"Error executing command: {e}")
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.FAILED,
            end_time=datetime.now(),
            error=str(e)
        )
        return False, [str(e)]

    finally:
        if process:  # Check if process was created
            try:
                if process.poll() is None:
                    process.terminate()
                    process.wait(timeout=5)
                else:
                    # Ensure reap even if already terminated
                    process.wait()
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()  # Final reap without timeout
            except Exception as e:
                logger.exception(f"Failed to clean up process for {job_id}: {e}")

        # Remove from running_processes only after reaping (consolidated here)
        with process_lock:
            if job_id in running_processes:
                del running_processes[job_id]

def kill_task(job_id: str) -> bool:
    """Kill a running task"""
    with process_lock:
        if job_id not in running_processes:
            return False
        process = running_processes[job_id]

    try:
        # Send SIGTERM to the process group
        os.killpg(process.pid, signal.SIGTERM)

        # Wait for process to terminate
        for _ in range(10):  # Increase retries for robustness
            if process.poll() is not None:
                break
            time.sleep(0.2)  # Slightly longer sleep

        # If still running, send SIGKILL to the group
        if process.poll() is None:
            os.killpg(process.pid, signal.SIGKILL)
            try:
                process.wait(timeout=5)  # Wait with timeout
            except subprocess.TimeoutExpired:
                process.wait()  # Final attempt without timeout to ensure reaping

        # Update operation status
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.KILLED,
            end_time=datetime.now()
        )

        return True

    except Exception as e:
        logger.exception(f"Error killing task {job_id}: {e}")
        return False

    finally:
        # Ensure removal even on failure
        with process_lock:
            if job_id in running_processes:
                del running_processes[job_id]


def suspend_task(job_id: str) -> bool:
    """Suspend a running task"""
    with process_lock:
        if job_id not in running_processes:
            return False
        process = running_processes[job_id]

    try:
        # Send SIGSTOP
        os.kill(process.pid, signal.SIGSTOP)

        # Update operation status
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.SUSPENDED
        )
        return True
    except Exception as e:
        logger.exception(f"Error suspending task {job_id}: {e}")
        return False

def resume_task(job_id: str) -> bool:
    """Resume a suspended task"""
    with process_lock:
        if job_id not in running_processes:
            return False
        process = running_processes[job_id]

    try:
        # Send SIGCONT
        os.kill(process.pid, signal.SIGCONT)

        # Update operation status
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.RUNNING
        )
        return True
    except Exception as e:
        logger.exception(f"Error resuming task {job_id}: {e}")
        return False

def get_operation_status(job_id: str) -> Optional[Dict[str, Any]]:
    """Get operation status, combining database and Celery information"""
    return operations.get_operation_status(job_id)

def get_operation(job_id: str) -> Optional[Dict[str, Any]]:
    """Get operation by ID"""
    return operations.get_operation(job_id)

def handle_task_success(job_id: str, structured_output: Dict[str, Any], message: str, count: int = None) -> Dict[str, Any]:
    """Helper function to handle successful task completion"""
    operations.update_operation(
        job_id=job_id,
        status=TaskStatus.COMPLETED,
        structured_output=structured_output,
        raw_output=[],  # Clear raw output on success
        end_time=datetime.now(),
        progress_perc=100
    )

    result = {
        "job_id": job_id,
        "success": True,
        "message": message
    }
    if count is not None:
        result["count"] = count
    return result

def handle_task_failure(job_id: str, error_message: str) -> Dict[str, Any]:
    """Helper function to handle task failure"""
    operations.update_operation(
        job_id=job_id,
        status=TaskStatus.FAILED,
        error=error_message,
        end_time=datetime.now()
    )

    return {
        "job_id": job_id,
        "success": False,
        "message": error_message
    }

def handle_task_exception(job_id: str, exception: Exception, context: str) -> Dict[str, Any]:
    """Helper function to handle task exceptions"""
    error_message = str(exception)
    logger.exception(f"Error in {context}: {exception}")
    return handle_task_failure(job_id, error_message)
