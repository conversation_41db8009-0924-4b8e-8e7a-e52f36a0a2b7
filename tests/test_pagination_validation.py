"""
Test pagination validation for TSM jobs endpoint.
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from app.main import app
from app.models.task import TaskStatus

client = TestClient(app)

# Mock API key for testing
TEST_API_KEY = "test_api_key"

@pytest.fixture
def mock_api_key():
    with patch("app.main.API_KEY", TEST_API_KEY):
        yield

@pytest.fixture
def auth_headers():
    return {"X-API-Key": TEST_API_KEY}

def test_pagination_validation_invalid_page(mock_api_key, auth_headers):
    """Test that requesting a page that doesn't exist returns 400 error."""
    job_id = "test-job-id"
    
    # Mock operation with completed status and chunked data
    mock_operation = {
        "_id": job_id,
        "status": TaskStatus.COMPLETED,
        "feature": "tech_detection",
        "structured_output": {
            "stats": {
                "total": 50,
                "total_chunks": 1,
                "chunk_size": 50
            }
        }
    }
    
    with patch("app.db.operations.get_operation_status") as mock_status, \
         patch("app.db.operations.get_operation") as mock_get_op:
        
        mock_status.return_value = mock_operation
        mock_get_op.return_value = mock_operation
        
        # Request page 10 when only 1 page exists (50 records with 1000 per page)
        response = client.get(
            f"/tsm/jobs/{job_id}?page=10&records_per_page=1000",
            headers=auth_headers
        )
        
        assert response.status_code == 400
        assert "Page 10 does not exist" in response.json()["detail"]
        assert "Total pages: 1" in response.json()["detail"]

def test_pagination_validation_valid_page(mock_api_key, auth_headers):
    """Test that requesting a valid page works correctly."""
    job_id = "test-job-id"
    
    # Mock operation with completed status and chunked data
    mock_operation = {
        "_id": job_id,
        "status": TaskStatus.COMPLETED,
        "feature": "tech_detection",
        "structured_output": {
            "stats": {
                "total": 50,
                "total_chunks": 1,
                "chunk_size": 50
            }
        }
    }
    
    # Mock chunk data
    mock_chunk_data = {
        "data": [{"url": f"https://example{i}.com", "technologies": []} for i in range(50)]
    }
    
    with patch("app.db.operations.get_operation_status") as mock_status, \
         patch("app.db.operations.get_operation") as mock_get_op, \
         patch("app.db.operations.get_tech_detection_chunk") as mock_chunk:
        
        mock_status.return_value = mock_operation
        mock_get_op.return_value = mock_operation
        mock_chunk.return_value = mock_chunk_data
        
        # Request page 1 (valid page)
        response = client.get(
            f"/tsm/jobs/{job_id}?page=1&records_per_page=1000",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["results"]["records"]) == 50
        assert data["results"]["pagination"]["page"] == 1
        assert data["results"]["pagination"]["total_pages"] == 1
        assert data["results"]["pagination"]["total_records"] == 50

def test_pagination_validation_multiple_pages(mock_api_key, auth_headers):
    """Test pagination validation with multiple pages."""
    job_id = "test-job-id"
    
    # Mock operation with 50 records
    mock_operation = {
        "_id": job_id,
        "status": TaskStatus.COMPLETED,
        "feature": "tech_detection",
        "structured_output": {
            "stats": {
                "total": 50,
                "total_chunks": 1,
                "chunk_size": 50
            }
        }
    }
    
    with patch("app.db.operations.get_operation_status") as mock_status, \
         patch("app.db.operations.get_operation") as mock_get_op:
        
        mock_status.return_value = mock_operation
        mock_get_op.return_value = mock_operation
        
        # With 10 records per page, we should have 5 pages
        # Page 6 should not exist
        response = client.get(
            f"/tsm/jobs/{job_id}?page=6&records_per_page=10",
            headers=auth_headers
        )
        
        assert response.status_code == 400
        assert "Page 6 does not exist" in response.json()["detail"]
        assert "Total pages: 5" in response.json()["detail"]
